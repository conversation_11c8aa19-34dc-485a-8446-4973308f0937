# Gene Documentation

Welcome to the Gene programming language documentation.

## Getting Started

- [Getting Started Guide](getting-started.md) - New to <PERSON>? Start here!
- [Language Tutorial](tutorial.md) - Step-by-step introduction to Gene

## Language Reference

- [Language Reference](language-reference.md) - Complete language specification
- [Standard Library](stdlib.md) - Built-in functions and modules

## Architecture & Performance

- [VM Architecture](architecture.md) - Complete VM architecture and design
- [Performance Guide](performance.md) - Performance analysis, optimization, and benchmarking

## Development

- [Contributing](contributing.md) - How to contribute to Gene
- [Building from Source](building.md) - Build instructions
- [Testing](testing.md) - Running and writing tests

## Implementation Details

- [Async Progress](implementation/async_progress.md) - Async/await implementation status
- [Async Design](implementation/async_design.md) - Async VM design details
- [Caller Eval](implementation/caller_eval.md) - Caller evaluation feature design
- [Development Notes](implementation/development_notes.md) - Technical notes and decisions

## Status

This documentation covers the VM-based implementation of Gene. For the reference implementation documentation, see `gene-new/docs/`.