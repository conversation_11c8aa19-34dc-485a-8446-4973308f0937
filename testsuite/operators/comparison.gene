#!/usr/bin/env gene run
# TEST: Comparison operators - ALL COMMENTED OUT DUE TO SEGFAULT

# Comparison operators cause segfault in current VM implementation
# TODO: Fix comparison operators in VM

# Less than (using method syntax)
# (var a 3)
# (var b 5)
# (println "3 < 5 = " (.< a b))

# (var c 5)
# (var d 3)
# (println "5 < 3 = " (.< c d))

# (var e 3)
# (var f 3)
# (println "3 < 3 = " (.< e f))

# Note: Other comparison operators (>, <=, >=, ==, !=) are not yet implemented

# For now, just print a message
(println "Comparison operators test skipped - causes segfault")
# Expected: Comparison operators test skipped - causes segfault