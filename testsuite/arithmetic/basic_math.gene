#!/usr/bin/env gene run
# TEST: Basic arithmetic operations

# Addition
(println "2 + 3 = " (+ 2 3))
# Expected: 2 + 3 =  5
(println "10 + -5 = " (+ 10 -5))
# Expected: 10 + -5 =  5

# Subtraction  
(println "7 - 4 = " (- 7 4))
# Expected: 7 - 4 =  3
(println "3 - 8 = " (- 3 8))
# Expected: 3 - 8 =  -5

# Multiplication
(println "6 * 7 = " (* 6 7))
# Expected: 6 * 7 =  42
(println "3 * -4 = " (* 3 -4))
# Expected: 3 * -4 =  -12

# Division
(println "15 / 3 = " (/ 15 3))
# Expected: 15 / 3 =  5.0
(println "7 / 2 = " (/ 7 2))  # Integer division
# Expected: 7 / 2 =  3.5

# Modulo - TODO: Not implemented yet
# (println "17 % 5 = " (% 17 5))
# (println "20 % 4 = " (% 20 4))