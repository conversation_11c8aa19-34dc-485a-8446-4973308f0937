#!/usr/bin/env gene run

# Test: Float literal parsing and operations

# Basic float literals
(println 3.14)
# Expected: 3.14
(println 0.5)
# Expected: 0.5
(println 42.0)
# Expected: 42.0

# Float arithmetic
(println (+ 1.5 2.5))
# Expected: 4.0
(println (- 10.0 3.7))
# Expected: 6.3
(println (* 2.5 4.0))
# Expected: 10.0
(println (/ 10.0 3.0))
# Expected: 3.333333333333333

# Float comparisons - commented out (causes segfault)
# (println (.< 3.14 4.0))

# Mixed int/float operations
(println (+ 1 2.5))
# Expected: 3.5
(println (* 3 1.5))
# Expected: 4.5