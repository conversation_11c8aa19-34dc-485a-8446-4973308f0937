#!/usr/bin/env gene run
# TEST: if/else expressions (requires 'else' keyword)

# Simple if with else keyword
(println "if true: " (if true "yes" else "no"))
# Expected: if true:  yes
(println "if false: " (if false "yes" else "no"))
# Expected: if false:  no

# if without else - commented out as it causes segfault
# (println "if true no else: " (if true "only if true"))
# (println "if false no else: " (if false "should not print"))

# Nested if - commented out (causes infinite loop)
# (var y 10)
# (println "nested if: " 
#   (if (.< y 5)
#     "less than 5"
#   else
#     (if (.< y 15)
#       "between 5 and 15"
#     else
#       "greater than 15")))