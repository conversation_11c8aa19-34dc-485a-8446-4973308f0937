#!/usr/bin/env gene run
# TEST: Basic function definitions and calls

# Simple function
(fn add [a b]
  (+ a b))

(println "add(3, 4) = " (add 3 4))
# Expected: add(3, 4) =  7

# Function with no parameters
(fn get_greeting []
  "Hello, World!")

(println "greeting: " (get_greeting))
# Expected: greeting:  Hello, World!

# Function with local variables
(fn calculate [x]
  (var doubled (* x 2))
  (var squared (* x x))
  (+ doubled squared))

(println "calculate(5) = " (calculate 5))
# Expected: calculate(5) =  35

# Nested function calls
(fn double [x] (* x 2))
(fn add_one [x] (+ x 1))

(println "double(add_one(3)) = " (double (add_one 3)))
# Expected: double(add_one(3)) =  8

# Recursive function - commented out (<= not implemented)
# (fn factorial [n]
#   (if (<= n 1)
#     1
#     (* n (factorial (- n 1)))))
# 
# (println "factorial(5) = " (factorial 5))