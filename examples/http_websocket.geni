#!/usr/bin/env gene run

import genex/mysql:DB
import genex/http/*
import genex/html/tags/*

# Prerequisites:
# A running database server
# A database ready to be used
# A user with proper access
var $ns/DB_HOST ($env "DB_HOST" "localhost")
var $ns/DB_USER ($env "DB_USER")
var $ns/DB_PASS ($env "DB_PASS")
var $ns/DB_NAME ($env "DB_NAME" "todos")

fn init_db _
  var conn (DB/open DB_HOST DB_USER DB_PASS DB_NAME)
  conn .exec """
    CREATE TABLE IF NOT EXISTS `todos`.`todos` (
      `id` INT NOT NULL AUTO_INCREMENT,
      `description` VARCHAR(255) NOT NULL,
      `status` INT NULL,
      PRIMARY KEY (`id`),
      UNIQUE INDEX `id_UNIQUE` (`id` ASC) VISIBLE
    )
  """

class Orm
  .ctor /conn

  .fn exec [query params...]
    /conn .exec query/.to_s params...

class Query
  .ctor /table

  .fn select /select...

  .fn join join
    /joins ||= []
    /joins .add join

  .fn where /where

  .fn order_by /order_by

  .fn limit /limit

  .fn to_s _
    var sql "SELECT "
    if /select
      sql .append (/select .join ",") " "
    else
      sql .append "* "

    sql .append "FROM " /table " "
    if /joins
      sql .append (/joins .join " ") " "

    if /where
      sql .append "WHERE " /where " "

    if /order_by
      sql .append "ORDER BY " /order_by " "

    if /limit
      sql .append "LIMIT " /limit

    = sql

class Record
  .on_extended
    fnx child
      child .on_member_missing
        fnx name
          if (Record/.members .contains name)
            /.parent ./ name

  fn /from_row row
    var props {}
    for [i v] in row
      $set props (@ (/columns ./ i)) v
    new self props

  fn /find id
    var query (new Query /table)
    query .where "id = ?"
    var data (/orm .exec query id)
    if (data/.size == 0)
      throw #"Not found: #{id}"
    else
      . /from_row data/0

  # Query all and convert each row to an instance of the model class
  fn /all _
    var query (new Query /table)
    var data (/orm .exec query)
    data .map
      row ->
        var props {}
        for [i v] in row
          $set props (@ (/columns ./ i)) v
        new self props

  fn /columns_except_id _
    var columns []
    for col in /columns
      if (col != "id")
        columns .add col
    columns

  .fn insert _
    var columns (/.class . /.class/columns_except_id)
    var sql #"
      INSERT INTO #{/.class/table} (#(columns .join ", "))
      VALUES(#((columns .map (col -> "?")) .join ", "))"
    var params (columns .map (col -> (./ col)))
    /.class/orm .exec sql params...

  .fn update _
    var sql #"UPDATE #{/.class/table} SET status = ? WHERE id = ?"
    /.class/orm .exec sql /status /id

  # If no id, insert into the table, else update existing row.
  .fn save _
    if not /id
      .insert
    else
      .update

  .fn delete _
    var sql #"DELETE FROM #{/.class/table} WHERE id = ?"
    /.class/orm .exec sql /id

class Todo < Record
  var /table "todos"
  var /columns ["id" "description" "status"]

  .ctor props
    /id = props/id
    /description = props/description
    /status = (props/status || 0)

  .fn done _
    /status/.to_s == "1"

  .fn set_done _
    /status = 1

  .fn set_todo _
    /status = 0

  .fn to_json _
    {
      ^id /id
      ^description /description
      ^status /status
    }

  .fn to_html _
    LI ^class #"py-6 px-2 border-b border-grey-darkest flex justify-between items-center relative todo__item #(if /.done "done")"
      DIV
        INPUT ^type "checkbox" ^id /id ^class "cbx hidden" ^checked /.done
        LABEL ^for /id ^class "text-xl cbx__child"
        LABEL ^for /id ^class "cbx__lbl text-white inline-block mt-1"
          /description
      BUTTON ^type "button" ^class "flex items-center delete-button absolute right-0" ^data-id /id
        SVG ^class "feather feather-x" ^xmlns "http://www.w3.org/2000/svg" ^width "24" ^height "24" ^viewBox "0 0 24 24"
            ^fill "none" ^stroke "currentColor" ^stroke-width "2" ^stroke-linecap "round" ^stroke-linejoin "round"
          LINE ^x1 "18" ^y1 "6" ^x2 "6" ^y2 "18"
          LINE ^x1 "6" ^y1 "6" ^x2 "18" ^y2 "18"

class App
  .ctor /port
    /middlewares = []

    # Open connection to the database
    var conn (DB/open DB_HOST DB_USER DB_PASS DB_NAME)
    # Create a instance of Orm with DB connection
    var orm (new Orm conn)
    # Set orm member on Record class which gets inherited by child classes of Record
    var Record/orm orm

  .fn construct_handler middleware_index
    if (middleware_index < /middlewares/.size)
      var middleware (/middlewares ./ middleware_index)
      var handler    (.construct_handler (middleware_index + 1))
      middleware handler
    else
      = /handler

  .fn enable_websocket [path handler]
    /websocket = {^path path ^handler handler}

  .fn start _
    println "Starting HTTP server at port" /port "..."

    var root_handler (.construct_handler 0)
    start_server /port root_handler ^websocket /websocket
    gene/run_forever

# Base class for controllers
class Controller
  .fn layout [title = "TODO App" content]
    HTML
      HEAD
        META ^charset "utf-8"
        META ^name "viewport" ^content "width=device-width, initial-scale=1"
        TITLE title
        LINK ^href "http://minimal-todo.surge.sh/src/assets/favicon/favicon-32x32.png" ^rel "icon" ^type "image/png" ^sizes "32x32"
        LINK ^href "http://minimal-todo.surge.sh/src/assets/favicon/favicon-16x16.png" ^rel "icon" ^type "image/png" ^sizes "16x16"
        LINK ^href "https://cdn.jsdelivr.net/npm/tailwindcss/dist/tailwind.min.css" ^rel "stylesheet"
        LINK ^href "https://fonts.googleapis.com/css?family=Raleway:300,400" ^rel "stylesheet"
        STYLE """
          body {
            font-family: "Raleway", sans-serif;
            background-color: #111;
            margin-bottom: 50px;
          }

          @media (max-width: 768px) {
            .github-corner {
              display: none;
            }
          }

          .github-corner:hover .octo-arm {
            animation: octocat-wave 560ms ease-in-out;
          }

          @keyframes octocat-wave {
            0%,
            100% {
              transform: rotate(0);
            }
            20%,
            60% {
              transform: rotate(-25deg);
            }
            40%,
            80% {
              transform: rotate(10deg);
            }
          }

          h1 span {
            color: #1dd1a1;
          }

          .input-wrapper input {
            transition: border-color 0.3s ease;
          }

          .input-wrapper input:focus {
            outline: none;
            border-color: #1dd1a1;
          }

          .input-wrapper .text-guide {
            right: 0;
            bottom: -8px;
            text-transform: uppercase;
          }

          .cbx__lbl {
            margin-left: 4px;
            vertical-align: middle;
            cursor: pointer;
            position: relative;
          }

          .cbx__lbl:after {
            content: "";
            width: 0%;
            height: 2px;
            background: #1dd1a1;
            position: absolute;
            left: 0;
            top: 50%;
            display: block;
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
          }

          li.done .cbx__lbl {
            color: #c8ccd4;
          }

          li.done .cbx__lbl:after {
            width: 100%;
          }

          li.done .cbx__child {
            border-color: transparent;
            background: #1dd1a1;
            animation: jelly 0.6s ease;
          }

          li.done .cbx__child:after {
            opacity: 1;
            transform: rotate(45deg) scale(1);
          }

          @keyframes jelly {
            from {
              transform: scale(1, 1);
            }
            30% {
              transform: scale(1.25, 0.75);
            }
            40% {
              transform: scale(0.75, 1.25);
            }
            50% {
              transform: scale(1.15, 0.85);
            }
            65% {
              transform: scale(0.95, 1.05);
            }
            75% {
              transform: scale(1.05, 0.95);
            }
            to {
              transform: scale(1, 1);
            }
          }

          .cbx__child {
            position: relative;
            top: 1px;
            width: 17px;
            height: 17px;
            border: 1px solid #c8ccd4;
            border-radius: 3px;
            vertical-align: middle;
            display: inline-block;
            transition: background 0.1s ease;
            cursor: pointer;
          }

          .cbx__child:after {
            content: "";
            position: absolute;
            top: 1px;
            left: 5px;
            width: 5px;
            height: 11px;
            opacity: 0;
            transform: rotate(45deg) scale(0);
            border-right: 2px solid #fff;
            border-bottom: 2px solid #fff;
            transition: all 0.3s ease;
            transition-delay: 0.15s;
          }

          .fade-enter-active,
          .fade-leave-active {
            transition: opacity 0.3s;
          }

          .fade-enter,
          .fade-leave-to {
            opacity: 0;
          }

          .todo__item:hover .delete-button {
            opacity: 1;
            visibility: visible;
          }

          .todo__item:last-child {
            border-bottom: none;
          }

          .delete-button {
            opacity: 0;
            visibility: hidden;
            transition: opacity 0.3s ease;
          }

          .delete-button svg {
            transition: all 0.3s ease;
            height: 20px;
          }

          .delete-button .feather {
            color: #fff;
          }

          .delete-button:hover .feather {
            color: #ff6b6b;
          }

          .delete-button:focus {
            outline: none;
            border: 1px dashed #ff6b6b;
          }

          .credit-disclaimer {
            color: #1dd1a1;
            position: fixed;
            height: 50px;
            bottom: 0px;
            left: 0px;
            right: 0px;
            margin-bottom: 0px;
          }
        """
      BODY ^class "text-center text-white"
        DIV ^id "app" "Loading..."
        # https://markus.oberlehner.net/blog/goodbye-webpack-building-vue-applications-without-webpack/
        LINK ^rel "modulepreload" ^href "https://unpkg.com/vue@3.0.4/dist/vue.runtime.esm-browser.js"
        LINK ^rel "modulepreload" ^href "https://unpkg.com/htm@3.0.4/dist/htm.module.js?modul"
        SCRIPT ^src "https://unpkg.com/vue@3.0.4"
        SCRIPT ^type "module" ^src "/todos/index.js"

        # (SCRIPT (genex/js/js
        #   (var ws (new WebSocket "ws://localhost:2080/ws"))
        #   (fn* createTodo _
        #     (var description ((($ "[name=description]").val)))
        #     (var payload {
        #       ^type "create"
        #       ^data^description description
        #     })
        #     (ws.send (JSON.stringify payload))
        #   )

        #   ((($ "li label.cbx__child").click) (fnx* e
        #     (var id ((($ this).attr) "for"))
        #     (var oldStatus (? ((($ ("input#" id)).is) ":checked") 1 0))
        #     (var newStatus (? (oldStatus == 1) 0 1))
        #     (var payload {
        #       ^type "update"
        #       ^data {
        #         ^id id
        #         ^status newStatus
        #       }
        #     })
        #     (ws.send (JSON.stringify payload))
        #   ))
        #   ((($ "li button.delete-button").click) (fnx* e
        #     (var id ((($ this).data) "id"))
        #     (var payload {
        #       ^type "update"
        #       ^data id
        #     })
        #     (ws.send (JSON.stringify payload))
        #   ))
        # ))

  .fn render args...
    respond ((.layout args...).to_s)

class HomeController < Controller
  .fn index req
    redirect "/todos"

class TodosController < Controller
  .fn index req
    var todos (Todo . Todo/all)
    .render
      UL ^class "list-reset"
        ... (todos .map (todo ->
          todo/.to_html
        ))

  .fn index_js req
    var todos ((Todo . Todo/all).to_json)
    respond #"""
      import { h } from 'https://unpkg.com/vue@3.0.4/dist/vue.runtime.esm-browser.js'
      import htm from 'https://unpkg.com/htm@3.0.4/dist/htm.module.js?module'

      var html = htm.bind(h)

      var todos = #{todos}
      var ws = new WebSocket("ws://localhost:2080/ws")

      var TodoComponent = {
        props: [
          "todo",
        ],
        methods: {
          toggle() {
            console.log("Updating...")
            if (this.todo.status == "1") {
              this.todo.status = "0"
            } else {
              this.todo.status = "1"
            }
            var payload = {
              type: "update",
              data: this.todo,
            }
            ws.send(JSON.stringify(payload))
          },
          delete() {
            console.log("Deleting...")
            var payload = {
              type: "delete",
              data: this.todo.id,
            }
            ws.send(JSON.stringify(payload))
          },
        },
        render() {
          console.log("Rendering TodoComponent...")
          console.log(this.todo)
          return html`
            <li class="py-6 px-2 border-b border-grey-darkest flex justify-between items-center relative todo__item">
              <div>
                <input type="checkbox" id="0" class="cbx hidden" v-model=${this.todo.status} onClick=${this.toggle}/>
                <label for="0" class="text-xl cbx__child"></label>
                <label for="0" class="cbx__lbl text-white inline-block mt-1 completed">${this.todo.description}</label>
              </div>
              <button type="button" class="flex items-center delete-button absolute right-0" onClick=${this.delete}>
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-x">
                  <line x1="18" y1="6" x2="6" y2="18"></line>
                  <line x1="6" y1="6" x2="18" y2="18"></line>
                </svg>
              </button>
            </li>
          `
        },
      }

      var TodosComponent = {
        data() {
          return {
            todos: todos
          }
        },
        render() {
          console.log("Rendering TodosComponent...")
          console.log(this.todos)
          return html`
            <div class="container mx-auto">
              <div class="mx-auto w-full md:w-1/2 py-8 px-4">
                <div class="flex items-center mb-6">
                  <h1 class="text-4xl text-white mr-6">
                    <span>MINIMAL</span> TO DO
                  </h1>
                  <img src="http://minimal-todo.surge.sh/logo.f4bfa240.svg" width="40" alt="" class="hidden md:block"/>
                </div>
                <div class="input-wrapper relative">
                  <input type="text" placeholder="What needs to be done today?" class="p-4 mb-4 w-full bg-transparent border-grey-light text-white border rounded"/>
                  <span class="text-guide text-gray-600 absolute text-xs" style="display: none;">Press Enter</span>
                </div>
                <ul class="list-reset">
                  ${
                    this.todos.map(todo => html`<${TodoComponent} todo=${todo}/>`)
                  }
                </ul>
              </div>
            </div>
          `
        },
      }

      Vue.createApp(TodosComponent).mount('#app')

    """ {^content-type "text/javascript"}

  # (.fn create req
  #   (var todo (new Todo req/.body_params))
  #   todo/.save
  #   (redirect "/todos")
  # )

  # (.fn update req
  #   (match [^id ^status] req/.body_params)
  #   (var todo (Todo . Todo/find id))
  #   ($set todo @status status)
  #   todo/.save
  #   # (redirect "/todos")
  #   (respond ({^status "success"}.to_json))
  # )

  # (.fn delete req
  #   (var id req/.body_params/id)
  #   (var todo (Todo . Todo/find id))
  #   (if not todo
  #     (throw #"Not found: #{id}")
  #   )
  #   todo/.delete
  #   (respond ({^status "success"}.to_json))
  # )

class Mapping
  .ctor [/path /controller /action /http_methods]

  # Return bool: whether the path/method/... of the request matches
  .fn match req
    &&
      req/.path == /path
      ||
        = /http_methods/.empty
        /http_methods .contains req/.method

class Router
  .ctor _
    /mappings = []

  .fn map [^options... path controller action = "index"]
    var http_methods []
    for method in ["GET" "POST" "PUT" "PATCH" "DELETE"]
      if (options ./ method)
        http_methods .add method
    /mappings .add (new Mapping path controller action http_methods)

  .fn call req
    for m in /mappings
      if (m .match req)
        var result ((new m/controller) . m/action req)
        if result
          return result

fn create_router _
  $tap (new Router)
    .map "/" HomeController
    # .map ^^POST   "/todos" TodosController :create
    # .map ^^PATCH  "/todos" TodosController :update
    # .map ^^DELETE "/todos" TodosController :delete
    .map          "/todos"          TodosController
    .map          "/todos/index.js" TodosController :index_js

class WebSocketHandler
  .fn call [ws payload]
    println "Received websocket message:" payload
    case payload/type
    when "create"
      var todo (new Todo payload/data)
      todo .save
      ws .send {}
    when "update"
      match [^id ^status] payload/data
      var todo (Todo . Todo/find id)
      todo/status = status
      todo .save
      ws .send {}
    when "delete"
      var todo (Todo . Todo/find payload/data)
      if todo
        todo .delete
      else
        println #"Not found: #{id}"
      ws .send {}
    else
      println "Not supported operation: " payload/type

$if_main
  init_db

  var port
    if ($cmd_args/.size > 1)
      = $cmd_args/1/.to_i
    else # elif/else/when/catch/finally/... are treated as a special word that is part of if/case/try statement that has same indentation
      = 2080

  # Alternative
  var port
    if
      $cmd_args/.size > 1
    then
      = $cmd_args/1/.to_i
    else # elif/else/when/catch/finally/... are treated as a special word that is part of if/case/try statement that has same indentation
      = 2080

  var app
    $tap (new App port)
      /handler = (create_router)
      /websocket = {
        ^path "/ws"
        ^handler (new WebSocketHandler)
      }

  # Alternative
  var app
    $tap (new App port)
      /handler = (create_router)
      /websocket =
        #Map
          ^path "/ws"
          ^handler (new WebSocketHandler)

  app .start

# More examples

# Example 1
# .gene
((fnx [a b]
  (a + b)
) 1 2)

# .geni
(fnx [a b]
  a + b
) 1 2

fnx [a b]
  a + b
; 1 2

# Example 2
# .gene
((fnx a a) 1)

# .geni
(fnx a a) 1

fnx a a; 1
fnx a a;
  = 1
