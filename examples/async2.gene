#!/usr/bin/env gene run

(fn read_async file
  (gene/File/read_async file)
)

(var filename "tests/fixtures/test.txt")
(println "Reading " filename "...")
(var future (read_async filename))
(var content)
(future .on_success
  (s -> (content = s)))
(await future)
(if not content
  # Q: how do we make sure callbacks can be registered after the future ends?
  # A: invoke callbacks when they are registered.
  (println "WARNING: async finished before callback is registered.")
  (exit 1)
)
(println "Content read:\n" content)
(assert (content/.trim == "line1\nline2"))
(println "Done.")
