#!/usr/bin/env gene run

#< Demo how command line arguments work
gene examples/parse_cmd_args.gene -d -i first -i second test.gene
#>#

($parse_cmd_args
  [
    program
    (option ^^toggle -d --debug)
    (option ^^multiple -i --include)
    (option -r --run)
    (argument ^!required file)
  ]
  $cmd_args   # The global variable that holds the command line argument array
)

(println "debug = "   debug)
(println "include = " include)
(println "file = "    file)
