#!/usr/bin/env gene run

# Simple fibonacci without advanced features
(fn fib n
  (if (n < 2)
    n
  else
    ((fib (n - 1)) + (fib (n - 2)))
  )
)

# Test with a fixed value
(var result (fib 10))
(println "fib(10) = " result)

# Test basic arithmetic
(var x 5)
(var y 3)
(println "5 + 3 = " (x + y))
(println "5 - 3 = " (x - y))
(println "5 * 3 = " (x * y))
(println "5 / 3 = " (x / y))

# Test pattern matching
(match a [10])
(println "Pattern match: a = " a)

# Test classes
(class Point
  (.ctor [x y]
    (/x = x)
    (/y = y)
  )
  (.fn distance _
    (($math/sqrt ((/x * /x) + (/y * /y))))
  )
)

(var p (new Point 3 4))
(println "Point: x=" p/x " y=" p/y)