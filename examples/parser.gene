# nil
nil

# boolean
true false

# numbers
1 2
-1 -2
1.2 2.3
-1.2
1e10 1E10
1e-10 1E-10
1.1e10
-1.1e-10

# string
"abc"
"中国"

# char, rune
'a' 'r'
'\r'
'中'

# symbol
a b ab
/ ?
中

# complex symbol
a/b
a/b/c
/a
a/

# array
[]
[1 2]
[1 a]
[1, 2] # comma is ignored

# map
{}
{^a 1}
{^a 1 ^b 2}
{^^a} {^a true} # equivalent
{^!a} {^a nil}  # equivalent
{^a^b 1} {^a {^b 1}} # equivalent
{^a^^b} {^a {^b true}} # equivalent

# gene
()
(a b) # gene type = a, gene children = [b]
(a ^b c d e) # gene type = a, gene properties = {^b c} gene children = [d e]
(a ^^b) (a ^b true) # equivalent
(a ^^b c) (a ^b true c) # equivalent
(a ^b^c d) (a ^b {^c d}) # equivalent

# placeholder
#_

# this is a line comment
#! this is another line comment that can serve as unix shebang
... #< comment ># ...
... #< comment #<< comment >># comment ># ...

# comment next parsed item
##1 2 # 1 is commented out, 2 is not
## [a b] c # [a b] is commented out, c is not
