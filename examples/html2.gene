#!/usr/bin/env gene run

(import genex/html/tags/*)
(import genex/html/style)

(var doc :(HTML
  (HEAD
    (TITLE "Test HTML")
  )
  (BODY
    (HEADER ^style {^font-size "22px"}
      "This is the header section"
    )
    (DIV ^style {^font-size "10px"}
      "First section"
    )
  )
))

(var css (@*
  (@ _ :BODY (style ^line-height 1.5))
))

# Apply css to doc
(css doc)

(println ((eval doc).to_s))
