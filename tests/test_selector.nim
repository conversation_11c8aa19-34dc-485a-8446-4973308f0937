import unittest, tables

import gene/types

import ./helpers

# Selector
# * <PERSON>rrow ideas from XPath/XSLT/CSS
#   * XPath: locate any node or group of nodes in a xml document
#   * XSLT: transform a xml document to another
#   * CSS: apply styles on any element or group of elements in a html document
#   * CSS Selectors: similar to XPath
# * Mode:
#   * Match first
#   * Match all
# * Flags:
#   * error_on_no_match: Throw error if none is matched
# * Types
#   * Index: 0,1,..-2,-1
#   * Index list
#   * Index range: 0..2
#   * Property name
#   * Property name list
#   * Property name pattern: /^test/
#   * Gene type: :$type
#   * Gene properties: :$props
#   * Gene property names: :$keys
#   * Gene property values: :$values
#   * Gene children: :$children
#   * Descendants: :$descendants - how does match work for this? self.gene.children and their descendants?
#   * Self and descendants: _
#   * Predicate (fnx it ...)
#   * Composite: [0 1 (range 3 5)]
#
# SelectorResult
# * Single value
# * Array or map or gene
#
# Define styles for gene value matched by a selector (like CSS).
# This should live outside the gene value.
# Inline styles can be defined for a gene. However it is not related to selectors.
# Shortcuts like those in css selectors
#   * id: &x matches (_ ^id "x")
#   * type: :y matches (y)
#   * tag (like css classes): ?t1?t2 matches (_ ^tags ["t1" "t2"])
#
# Transform a gene value based on selectors and actions (like XSLT)
# Should support non-gene output, e.g. raw strings.
# Actions:
#   * Copy value matched by selector to output
#   * Call callback with value, add result to output
#
# Distinguish predicates, transformers and callbacks etc:
# * Predicates return special list of path=value or value
# * Callbacks' does not return special list, thus discarded

# @p        <=> (@ "p")
# (@p)      <=> ((@ "p"))       <=> /p        <=> (self ./p)
# (@p)      <=> ((@ "p") self)
# (/p = 1)  <=> ((@ "p") = 1)   <=> (self ./p = 1)
# (/p += 1) <=> (/p = (/p + 1)) <=> ((@ "p") = ((@ "p") + 1))

# (./p)     <=> (self ./p)      <=> (self ./ "p")
# (/p = 1)  <=> ($set self @p 1)

# (@ "test")             # target["test"]
# @test                  # target["test"]
# (@ 0 "test")           # target[0]["test"]
# @0/test                # target[0]["test"]
# (@ (@ 0) "test")       # target[0]["test"]
# (@ [0 1] "test")       # target[0, 1]["test"]
# (@ (range 0 3) :type)  # target[0..3].type
# (@* [0 "test"] [1 "another"])  # target[0]["test"] + target[1]["another"]
#
# /0/test                   # ((@ 0 "test") self)
# (./ 0 "test")             # ((@ 0 "test") self)
# (./0/test)                # ((@ 0 "test") self)
# (./ :type)                # ((@ :type) self)
# (obj ./ 0 "test")         # ((@ 0 "test") obj)
# ((@* 0 1) self)
# (./ 0 "test" = "value")   # (assign self (@ 0 "test") "value")
# (./test)                  # ((@ "test") self)
# (./first/second)          # ((@ "first" "second") self)
#
# * Search
# * Update
# * Remove

# TODO: Selector functionality is not yet implemented in the VM
# These tests are commented out until the following features are added:
# - ./ operator for property/index access
# - @ operator for selector expressions
# - @test shorthand for (@ "test")
# - $set and $with operators
# - Range selectors
# - Instance property access syntax (/property = value)

# All tests below are commented out until selector support is implemented

# Basic property access using / syntax works, but ./ selector syntax is not implemented
# Let's test the basic / syntax that is already supported:

test_vm """
  (var x {^a 1})
  x/a
""", 1

test_vm """
  (var x [1 2 3])
  x/0
""", 1

test_vm """
  (var x [1 2 3])
  x/2
""", 3

# Testing the ./ selector operator:

test_vm """
  (./ {^a "A"} "a")
""", "A"

test_vm """
  (./ {} "a")
""", NIL

test_vm """
  (./ {} "a" 1)
""", 1

test_vm """
  ({^a "A"} ./a)
""", "A"

# This test uses _ to create a gene expression, which requires different syntax
# test_vm """
#   ((_ ^a "A") ./ "a")
# """, "A"

test_vm """
  ([1 2] ./ 0)
""", proc(r: Value) =
  check r == 1.to_value()

test_vm """
  ([1 2] ./0)
""", 1

# test_vm """
#   ([0 1 2 -2 -1] ./ (0 .. 1))
# """, @[0, 1]

# test_vm """
#   ([0 1 2 -2 -1] ./ (0 .. -2))
# """, @[0, 1, 2, -2]

# test_vm """
#   ([0 1 2 -2 -1] ./ (-2 .. -1))
# """, @[-2, -1]

# test_vm """
#   ([0 1 2 -2 -1] ./ (-1 .. -1))
# """, @[-1]

# test_vm """
#   ([0 1 2 -2 -1] ./ (6 .. -1))
# """, @[]

# test_vm """
#   ([] ./ (0 .. 1))
# """, @[]

# test_vm """
#   ([] ./ (-2 .. -1))
# """, @[]

# test_vm """
#   ([1] ./ (-2 .. -1))
# """, @[1]

# test_vm """
#   ((_ 0 1 2 -2 -1) ./ (0 .. -2))
# """, @[0, 1, 2, -2]

# test_vm """
#   ((_) ./ (-2 .. -1))
# """, @[]

# test_vm """
#   ((_) ./ (0 .. -2))
# """, @[]

# test_vm """
#   ((_ 1) ./ (-2 .. -1))
# """, @[1]

test_vm """
  ((@ "test") {^test 1})
""", 1

# This test uses @test shorthand which requires special parsing
# For now, @test creates a selector that needs to be applied differently
# test_vm """
#   (@test {^test 1})
# """, 1

# test_vm """
#   ((@ "test" 0) {^test [1]})
# """, 1

# test_vm """
#   (@test/0 {^test [1]})
# """, 1

# test_vm """
#   (@0/test [{^test 1}])
# """, 1

# test_vm """
#   ([{^test 1}] ./ 0 "test")
# """, 1

# test_vm """
#   ([{^test 1}] ./0/test)
# """, 1

# test_vm """
#   ($with [{^test 1}]
#     (./ 0 "test")
#   )
# """, 1

# test_vm """
#   (var a [0])
#   (a/0 = 1)
#   a/0
# """, 1

# test_vm """
#   (var a [0])
#   a/-1
# """, 0

# test_vm """
#   ($with [{^test 1}]
#     (./0/test)
#   )
# """, 1

test_vm """
  (var a {})
  ($set a (@ "test") 1)
  ((@ "test") a)
""", 1

test_vm """
  (var a [0])
  ($set a @0 1)
  a
""", proc(r: Value) =
  check r.kind == VkArray
  check r.ref.arr.len == 1
  check r.ref.arr[0] == 1.to_value()

# test_vm """
#   (class A)
#   (var a (new A))
#   ($set a @test 1)
#   (@test a)
# """, 1

# test_vm """
#   (class A
#     (.fn test x
#       ($set @x x)
#     )
#   )
#   (var a (new A))
#   (a .test 1)
#   a/x
# """, 1

# test_vm """
#   (class A
#     (.ctor []
#       (/description = "Class A")
#     )
#   )
#   (new A)
# """, proc(r: Value) =
#   check r.instance_props["description"] == "Class A"

# test_vm """
#   ((@ 0) [1 2])
# """, 1

# test_vm """
#   ((@ 0 "test") [{^test 1}])
# """, 1

# test_vm """
#   ((@ (@ 0)) [1 2])
# """, 1

# test_vm """
#   ((@ [0 1]) [1 2])
# """, @[1, 2]

# test_vm """
#   ((@ ["a" "b"]) {^a 1 ^b 2 ^c 3})
# """, @[1, 2]

# test_vm """
#   ((@* 0 1) [1 2])
# """, @[1, 2]

# test_vm """
#   (class A
#     (.fn test _
#       1
#     )
#   )
#   ((@. "test") (new A))
# """, 1

# test_vm """
#   (class A
#     (.fn test _
#       1
#     )
#   )
#   (@.test (new A))
# """, 1

# test_vm """
#   (class A
#     (.fn test _
#       1
#     )
#   )
#   (@0/.test [(new A)])
# """, 1

# test_vm """
#   ((@ :TEST 0)
#     (_ (:TEST 1))
#   )
# """, @[1]

# test_core """
#   (((@ _)
#     (_ (:TEST 1))
#     # Matches
#     # self: (_ (:TEST 1))
#     # descendants: (:TEST 1), 1
#   ).size)
# """, 3

# test_vm """
#   (var a)
#   (fn f v
#     (a = v)
#     (:void)
#   )
#   ((@ 0 f) [123])
#   a
# """, 123

# test_core """
#   ((@ 0 gene/inc) [1])
# """, @[2]

# test_vm """
#   ([] ./ 0 ^default 123)
# """, 123

# test_vm """
#   ([] ./0 ^default 123)
# """, 123

# test_vm """
#   (@0 [] ^default 123)
# """, 123