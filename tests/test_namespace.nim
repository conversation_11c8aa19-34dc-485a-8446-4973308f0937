import unittest

import gene/types

import ./helpers

# Namespace related metaprogramming:
#
# * Namespace.member_defined (called when a member is defined or re-defined)
# * Namespace.member_removed
# * Namespace.on_member_missing (invoked only if <some_ns>/something is invoked and something is not defined)
# * Namespace.has_member - it should be consistent with member_missing

# Basic namespace creation
test_vm "(ns test)", proc(r: Value) =
  check r.ref.ns.name == "test"

# Namespace access
test_vm """
  (ns n)
  n
""", proc(r: Value) =
  check r.ref.ns.name == "n"

# Multiple namespaces in same scope
test_vm """
  (ns n)
  (ns m)
  m
""", proc(r: Value) =
  check r.ref.ns.name == "m"

# Variables in namespace scope
test_vm """
  (ns n)
  (var a 1)
  a
""", 1

# Nested namespace creation - needs to be fixed to handle body compilation
# test_vm """
#   (ns n
#     (ns m)
#   )
#   n/m
# """, proc(r: Value) =
#   check r.ref.ns.name == "m"

# The following tests require features not yet implemented in VM:
# - Classes
# - Complex symbol handling in var definitions (n/a)
# - Namespace path resolution (n/m)
# - The 'global' symbol
# - Member missing handlers (.on_member_missing)
# - $ns syntax
# - Proxy functionality
# - /a syntax for namespace variables

# Classes not yet implemented in VM
# test_vm """
#   (ns n
#     (class A)
#   )
#   n/A
# """, proc(r: Value) =
#   check r.class.name == "A"

# Namespace variable definition with / prefix not supported
# test_vm """
#   (ns n
#     (var /a 1)
#   )
#   n/a
# """, 1

# Classes not yet implemented in VM
# test_vm """
#   (ns n)
#   (class n/A)
#   n/A
# """, proc(r: Value) =
#   check r.class.name == "A"

# Complex symbols in var not yet supported
# test_vm """
#   (ns n)
#   (var n/test 1)
#   n/test
# """, 1

# Nested namespace creation outside body not yet supported
# test_vm """
#   (ns n)
#   (ns n/m)
#   n/m
# """, proc(r: Value) =
#   check r.ref.ns.name == "m"

# Classes not yet implemented in VM
# test_vm """
#   (ns n)
#   (ns n/m
#     (class A)
#   )
#   n/m/A
# """, proc(r: Value) =
#   check r.class.name == "A"

# Global namespace access not yet implemented
# test_vm """
#   global
# """, proc(r: Value) =
#   check r.ref.ns.name == "global"

# Classes not yet implemented in VM
# test_vm """
#   (class global/A)
#   global/A
# """, proc(r: Value) =
#   check r.class.name == "A"

# Global variable access - complex symbols not supported
# test_vm """
#   (var global/a 1)
#   a
# """, 1

# Classes not yet implemented in VM
# test_vm """
#   (class A
#     (fn f a a)
#   )
#   (A/f 1)
# """, 1

# Classes not yet implemented in VM
# test_vm """
#   (ns n
#     (class A)
#     (ns m
#       (class B < A)
#     )
#   )
#   n/m/B
# """, proc(r: Value) =
#   check r.class.name == "B"

# Member missing handlers not yet implemented in VM
# test_vm """
#   (ns n
#     (.on_member_missing
#       (fnx name
#         (if (name == "test")
#           1
#         else
#           # What should we do here, in order to pass to the next namespace to search for the name?
#           # Option 1: ($get_member /.parent name)
#           # Option 2: ($not_found name)
#           # Option 3: (throw (new MemberNotFound name))
#         )
#       )
#     )
#   )
#   n/test
# """, 1

# Member missing handlers not yet implemented in VM
# test_vm """
#   (ns n
#     (.on_member_missing
#       (fnx name
#         ("" /.name "/" name)
#       )
#     )
#   )
#   n/test
# """, "n/test"

# Classes and member missing handlers not yet implemented in VM
# test_vm """
#   (class C
#     (.on_member_missing
#       (fnx name
#         ("" /.name "/" name)
#       )
#     )
#   )
#   C/test
# """, "C/test"

# Member missing handlers not yet implemented in VM
# test_vm """
#   (ns n
#     (.on_member_missing
#       (fnx name
#         (if (name == "a")
#           1
#         )
#       )
#     )
#     (.on_member_missing
#       (fnx name
#         (if (name == "b")
#           2
#         )
#       )
#     )
#   )
#   (n/a + n/b)
# """, 3

# $ns syntax not yet implemented in VM
# test_vm """
#   ($ns/a = 1)
#   (a = 2)
#   $ns/a
# """, 2

# $ns syntax not yet implemented in VM
# test_vm """
#   (var a 1)
#   ($ns/a = 1)
#   (a = 2)
#   $ns/a
# """, 1

# Proxy functionality not yet implemented in VM
# test_vm """
#   (ns n)
#   (ns m
#     (var a 1)
#   )
#   (n .proxy :a m)
#   n/a
# """, 1