{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "name": "(gdb) test_wip",
            "type": "cppdbg",
            "request": "launch",
            "program": "${workspaceRoot}/bin/test_wip",
            "args": [],
            // nim c --debugger:native --stackTrace:on -d:useMalloc --mm:orc --out:bin/test_wip tests/test_wip.nim
            "preLaunchTask": "nimbuild - test_wip",
            "stopAtEntry": false,
            "cwd": "${workspaceRoot}",
            "environment": [],
            "externalConsole": false,
            "MIMode": "gdb",
            "miDebuggerPath": "nim-gdb",
            "setupCommands": [
                {
                    "description": "Enable pretty-printing for gdb",
                    "text": "-enable-pretty-printing",
                    "ignoreFailures": true
                }
            ],
            "logging": {
                "engineLogging": true
            },
        },
        {
            "name": "(gdb) Launch",
            "type": "cppdbg",
            "request": "launch",
            "program": "${workspaceRoot}/bin/gene",
            "args": ["${workspaceRoot}/bin/render_html", "${workspaceRoot}/examples/html.gene"],
            "preLaunchTask": "nimbuild",
            "stopAtEntry": false,
            "cwd": "${workspaceRoot}",
            "environment": [],
            "externalConsole": false,
            "MIMode": "gdb",
            "miDebuggerPath": "nim-gdb",
            "setupCommands": [
                {
                    "description": "Enable pretty-printing for gdb",
                    "text": "-enable-pretty-printing",
                    "ignoreFailures": true
                }
            ],
            "logging": {
                "engineLogging": true
            },
        },
    ]
}